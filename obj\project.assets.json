{"version": 3, "targets": {"net9.0-windows7.0": {"DalamudPackager/11.0.0": {"type": "package", "build": {"build/DalamudPackager.props": {}, "build/DalamudPackager.targets": {}}, "buildMultiTargeting": {"buildMultiTargeting/DalamudPackager.props": {}}}}}, "libraries": {"DalamudPackager/11.0.0": {"sha512": "bjT7XUlhIJSmsE/O76b7weUX+evvGQctbQB8aKXt94o+oPWxHpCepxAGMs7Thow3AzCyqWs7cOpp9/2wcgRRQA==", "type": "package", "path": "dalamudpackager/11.0.0", "files": [".nupkg.metadata", ".signature.p7s", "build/DalamudPackager.props", "build/DalamudPackager.targets", "buildMultiTargeting/DalamudPackager.props", "dalamudpackager.11.0.0.nupkg.sha512", "dalamudpackager.nuspec", "tasks/net48/DalamudPackager.dll", "tasks/net48/Newtonsoft.Json.dll", "tasks/net48/YamlDotNet.dll", "tasks/netstandard2.1/DalamudPackager.dll", "tasks/netstandard2.1/Newtonsoft.Json.dll", "tasks/netstandard2.1/YamlDotNet.dll"]}}, "projectFileDependencyGroups": {"net9.0-windows7.0": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> >= 2.1.14"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\CCDraw\\PvPLinePlugin\\PvPLinePlugin.csproj", "projectName": "PvPLinePlugin", "projectPath": "C:\\Users\\<USER>\\Desktop\\CCDraw\\PvPLinePlugin\\PvPLinePlugin.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\CCDraw\\PvPLinePlugin\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0-windows"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreLockProperties": {"restorePackagesWithLockFile": "true"}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "dependencies": {"DalamudPackager": {"target": "Package", "version": "[2.1.14, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "logs": [{"code": "NU1601", "level": "Warning", "warningLevel": 1, "message": "Dependency specified was <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (>= 2.1.14) but ended up with <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 11.0.0.", "libraryId": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "targetGraphs": ["net9.0-windows7.0"]}]}