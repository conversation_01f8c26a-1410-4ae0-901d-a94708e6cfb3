using Dalamud.Configuration;
using Dalamud.Plugin;
using System;
using System.Numerics;

namespace PvPLinePlugin;

public enum IndicatorType
{
    Lines,
    Outlines,
    Nameplates,
    Icons,
    DirectionalArrows,
    HealthBars,
    Combination
}

[Serializable]
public class Configuration : IPluginConfiguration
{
    public int Version { get; set; } = 0;
    public bool Enabled { get; set; } = true;

    #region Visual Indicator Settings
    public IndicatorType IndicatorType { get; set; } = IndicatorType.Lines;
    public Vector4 LineColor { get; set; } = new Vector4(1.0f, 0.0f, 0.0f, 1.0f);
    public float LineThickness { get; set; } = 2.0f;
    public bool ColorCodeByRole { get; set; } = true;

    // Alternative indicator settings
    public float OutlineThickness { get; set; } = 3.0f;
    public float IconSize { get; set; } = 20.0f;
    public float ArrowSize { get; set; } = 15.0f;
    public bool ShowDirectionalArrows { get; set; } = true;
    public bool PulseIndicators { get; set; } = false;
    #endregion

    #region Distance Settings
    public float MaxDistance { get; set; } = 50.0f;
    public bool ShowDistance { get; set; } = true;
    #endregion

    #region PvP Settings
    public bool OnlyInPvP { get; set; } = true;
    public bool ShowInCombatOnly { get; set; } = false;
    #endregion

    #region Player Information
    public bool ShowPlayerNames { get; set; } = false;
    public bool ShowPlayerJobs { get; set; } = true;
    public bool ShowJobIcons { get; set; } = false;
    public bool ShowStatusEffects { get; set; } = true;
    public bool ShowOnlyImportantStatus { get; set; } = true;
    public bool ShowDefensiveBuffs { get; set; } = true;
    #endregion

    #region Low Health Settings
    public bool ShowLowHealthIndicator { get; set; } = true;
    public float LowHealthThreshold { get; set; } = 25.0f;
    public Vector4 LowHealthLineColor { get; set; } = new Vector4(1.0f, 0.8f, 0.0f, 1.0f);
    public float LowHealthLineThickness { get; set; } = 5.0f;
    public bool ShowHealthPercentage { get; set; } = true;
    public bool PulseKillableTargets { get; set; } = true;
    #endregion

    #region Enemy/Ally Detection
    public bool ShowAllies { get; set; } = false;
    public bool ShowEnemies { get; set; } = true;
    public Vector4 AllyLineColor { get; set; } = new Vector4(0.0f, 1.0f, 0.0f, 0.8f);
    public float AllyLineThickness { get; set; } = 2.0f;
    public bool ShowAllyEnemyIndicator { get; set; } = true;
    public bool DifferentColorsForAllies { get; set; } = true;
    #endregion

    #region Focus Target Settings
    public bool EnableFocusTarget { get; set; } = true;
    public string FocusTargetName { get; set; } = string.Empty;
    public Vector4 FocusTargetColor { get; set; } = new Vector4(1.0f, 1.0f, 0.0f, 1.0f); // Yellow
    public float FocusTargetThickness { get; set; } = 5.0f;
    public bool FocusTargetPulse { get; set; } = true;
    public bool FocusTargetAlwaysVisible { get; set; } = true; // Show even if outside normal distance
    public bool ShowFocusTargetInfo { get; set; } = true;
    public IndicatorType FocusTargetIndicatorType { get; set; } = IndicatorType.Lines;
    #endregion

    public void Save() => Plugin.PluginInterface.SavePluginConfig(this);
}
