using System;
using System.Numerics;
using Dalamud.Interface.Windowing;
using ImGuiNET;

namespace PvPLinePlugin.Windows;

public class ConfigWindow : Window, IDisposable
{
    private Configuration Configuration;
    private Plugin Plugin;

    public ConfigWindow(Plugin plugin) : base("PvP Line Plugin Configuration###PvPLinePluginConfig")
    {
        Flags = ImGuiWindowFlags.NoCollapse;

        Size = new Vector2(650, 700);
        SizeCondition = ImGuiCond.FirstUseEver;

        Configuration = plugin.Configuration;
        Plugin = plugin;
    }

    public void Dispose() { }

    public override void Draw()
    {
        // Main enable/disable at the top
        var enabled = Configuration.Enabled;
        if (ImGui.Checkbox("Enable PvP Lines", ref enabled))
        {
            Configuration.Enabled = enabled;
            Configuration.Save();
        }
        if (ImGui.IsItemHovered())
        {
            ImGui.SetTooltip("Master on/off switch for the entire plugin.\nDisable when you don't want any lines displayed.");
        }

        ImGui.Separator();
        ImGui.Spacing();

        // Tab bar for organized settings
        if (ImGui.BeginTabBar("PvPLinePluginTabs"))
        {
            DrawAppearanceTab();
            DrawDistanceTab();
            DrawPvPSettingsTab();
            DrawEnemyAllyTab();
            DrawJobRoleTab();
            DrawLowHealthTab();
            DrawFocusTargetTab();
            DrawPlayerListTab();

            ImGui.EndTabBar();
        }
    }

    private void DrawAppearanceTab()
    {
        if (ImGui.BeginTabItem("Appearance"))
        {
            ImGui.TextWrapped("Customize how enemy indicators look on your screen.");
            ImGui.Spacing();

            // Indicator Type Selection
            ImGui.Text("Indicator Type:");
            var currentIndicator = (int)Configuration.IndicatorType;
            var indicatorNames = new[] { "Lines", "Outlines", "Nameplates", "Icons", "Directional Arrows", "Health Bars", "Combination" };

            if (ImGui.Combo("##IndicatorType", ref currentIndicator, indicatorNames, indicatorNames.Length))
            {
                Configuration.IndicatorType = (IndicatorType)currentIndicator;
                Configuration.Save();
            }
            if (ImGui.IsItemHovered())
            {
                ImGui.SetTooltip("Choose how you want to see enemy indicators:\n" +
                               "• Lines: Draw lines from you to enemies (current)\n" +
                               "• Outlines: Colored outlines around enemy players\n" +
                               "• Nameplates: Enhanced nameplates above enemies\n" +
                               "• Icons: Small icons showing enemy positions\n" +
                               "• Directional Arrows: Arrows pointing to off-screen enemies\n" +
                               "• Health Bars: Floating health bars above enemies\n" +
                               "• Combination: Mix of multiple indicator types");
            }

            ImGui.Spacing();
            ImGui.Separator();
            ImGui.Spacing();

            // Show settings based on selected indicator type
            switch (Configuration.IndicatorType)
            {
                case IndicatorType.Lines:
                    DrawLineSettings();
                    break;
                case IndicatorType.Outlines:
                    DrawOutlineSettings();
                    break;
                case IndicatorType.Nameplates:
                    DrawNameplateSettings();
                    break;
                case IndicatorType.Icons:
                    DrawIconSettings();
                    break;
                case IndicatorType.DirectionalArrows:
                    DrawArrowSettings();
                    break;
                case IndicatorType.HealthBars:
                    DrawHealthBarSettings();
                    break;
                case IndicatorType.Combination:
                    DrawCombinationSettings();
                    break;
            }

            ImGui.EndTabItem();
        }
    }

    private void DrawLineSettings()
    {
        ImGui.Text("Line Settings:");
        var lineColor = Configuration.LineColor;
        if (ImGui.ColorEdit4("Line Color", ref lineColor))
        {
            Configuration.LineColor = lineColor;
            Configuration.Save();
        }
        if (ImGui.IsItemHovered())
        {
            ImGui.SetTooltip("Choose the color and transparency of lines.\nRecommended: Red for aggression, Yellow for visibility, Blue for subtlety.\nLower alpha (transparency) makes lines less intrusive.");
        }

        var lineThickness = Configuration.LineThickness;
        if (ImGui.SliderFloat("Line Thickness", ref lineThickness, 1.0f, 10.0f))
        {
            Configuration.LineThickness = lineThickness;
            Configuration.Save();
        }
        if (ImGui.IsItemHovered())
        {
            ImGui.SetTooltip("Controls how thick the lines appear.\n1-2px: Subtle, minimal\n3-4px: Good balance\n5-7px: High visibility\n8-10px: Maximum visibility but may be distracting");
        }

        var pulseIndicators = Configuration.PulseIndicators;
        if (ImGui.Checkbox("Pulse Effect", ref pulseIndicators))
        {
            Configuration.PulseIndicators = pulseIndicators;
            Configuration.Save();
        }
        if (ImGui.IsItemHovered())
        {
            ImGui.SetTooltip("Add a pulsing animation effect to lines for better visibility.");
        }
    }

    private void DrawOutlineSettings()
    {
        ImGui.Text("Outline Settings:");

        var lineColor = Configuration.LineColor;
        if (ImGui.ColorEdit4("Outline Color", ref lineColor))
        {
            Configuration.LineColor = lineColor;
            Configuration.Save();
        }

        var outlineThickness = Configuration.OutlineThickness;
        if (ImGui.SliderFloat("Outline Thickness", ref outlineThickness, 1.0f, 10.0f))
        {
            Configuration.OutlineThickness = outlineThickness;
            Configuration.Save();
        }
        if (ImGui.IsItemHovered())
        {
            ImGui.SetTooltip("Thickness of the outline around enemy players.");
        }
    }

    private void DrawNameplateSettings()
    {
        ImGui.Text("Nameplate Settings:");

        var lineColor = Configuration.LineColor;
        if (ImGui.ColorEdit4("Nameplate Color", ref lineColor))
        {
            Configuration.LineColor = lineColor;
            Configuration.Save();
        }

        ImGui.TextWrapped("Enhanced nameplates will appear above enemy players with job icons and health information.");
    }

    private void DrawIconSettings()
    {
        ImGui.Text("Icon Settings:");

        var iconSize = Configuration.IconSize;
        if (ImGui.SliderFloat("Icon Size", ref iconSize, 10.0f, 50.0f))
        {
            Configuration.IconSize = iconSize;
            Configuration.Save();
        }

        var lineColor = Configuration.LineColor;
        if (ImGui.ColorEdit4("Icon Color", ref lineColor))
        {
            Configuration.LineColor = lineColor;
            Configuration.Save();
        }

        ImGui.TextWrapped("Small icons will appear on screen showing enemy positions and job types.");
    }

    private void DrawArrowSettings()
    {
        ImGui.Text("Directional Arrow Settings:");

        var arrowSize = Configuration.ArrowSize;
        if (ImGui.SliderFloat("Arrow Size", ref arrowSize, 10.0f, 40.0f))
        {
            Configuration.ArrowSize = arrowSize;
            Configuration.Save();
        }

        var lineColor = Configuration.LineColor;
        if (ImGui.ColorEdit4("Arrow Color", ref lineColor))
        {
            Configuration.LineColor = lineColor;
            Configuration.Save();
        }

        ImGui.TextWrapped("Arrows will point toward off-screen enemies from the edges of your screen.");
    }

    private void DrawHealthBarSettings()
    {
        ImGui.Text("Health Bar Settings:");

        var lineColor = Configuration.LineColor;
        if (ImGui.ColorEdit4("Health Bar Color", ref lineColor))
        {
            Configuration.LineColor = lineColor;
            Configuration.Save();
        }

        ImGui.TextWrapped("Floating health bars will appear above enemy players showing their current HP.");
    }

    private void DrawCombinationSettings()
    {
        ImGui.Text("Combination Settings:");
        ImGui.TextWrapped("Mix and match multiple indicator types. Configure each type above by temporarily switching to it.");

        var showDirectionalArrows = Configuration.ShowDirectionalArrows;
        if (ImGui.Checkbox("Include Directional Arrows", ref showDirectionalArrows))
        {
            Configuration.ShowDirectionalArrows = showDirectionalArrows;
            Configuration.Save();
        }

        ImGui.TextWrapped("Note: Combination mode will use settings from each individual indicator type.");
    }

    private void DrawDistanceTab()
    {
        if (ImGui.BeginTabItem("Distance"))
        {
            ImGui.TextWrapped("Control how far lines reach and distance information display.");
            ImGui.Spacing();

            var maxDistance = Configuration.MaxDistance;
            if (ImGui.SliderFloat("Max Distance (yalms)", ref maxDistance, 10.0f, 100.0f))
            {
                Configuration.MaxDistance = maxDistance;
                Configuration.Save();
            }
            if (ImGui.IsItemHovered())
            {
                ImGui.SetTooltip("Only draws lines to enemies within this distance.\n20-30y: Close combat (melee DPS, tanks)\n40-50y: Balanced for most jobs\n60-80y: Long-range (casters, ranged DPS)\n100y: Maximum awareness (may cause clutter)");
            }

            var showDistance = Configuration.ShowDistance;
            if (ImGui.Checkbox("Show Distance Text", ref showDistance))
            {
                Configuration.ShowDistance = showDistance;
                Configuration.Save();
            }
            if (ImGui.IsItemHovered())
            {
                ImGui.SetTooltip("Displays actual distance in yalms next to each line.\nUseful for learning optimal engagement ranges.\nDisable to reduce screen clutter.");
            }

            ImGui.EndTabItem();
        }
    }

    private void DrawPvPSettingsTab()
    {
        if (ImGui.BeginTabItem("PvP Settings"))
        {
            ImGui.TextWrapped("Configure when and how the plugin activates in PvP content.");
            ImGui.Spacing();

            var onlyInPvP = Configuration.OnlyInPvP;
            if (ImGui.Checkbox("Only Show in PvP Zones", ref onlyInPvP))
            {
                Configuration.OnlyInPvP = onlyInPvP;
                Configuration.Save();
            }
            if (ImGui.IsItemHovered())
            {
                ImGui.SetTooltip("Restricts plugin to PvP areas only (Frontlines, Rival Wings, Crystalline Conflict, Wolves' Den).\nRecommended: Keep enabled to avoid marking friendly players as enemies in PvE content.");
            }

            var showInCombatOnly = Configuration.ShowInCombatOnly;
            if (ImGui.Checkbox("Only Show During Combat", ref showInCombatOnly))
            {
                Configuration.ShowInCombatOnly = showInCombatOnly;
                Configuration.Save();
            }
            if (ImGui.IsItemHovered())
            {
                ImGui.SetTooltip("Lines only appear when you're actively in combat (hotbars turn red).\nEnable: Reduces visual noise during downtime\nDisable: Constant enemy awareness and positioning");
            }

            var showPlayerNames = Configuration.ShowPlayerNames;
            if (ImGui.Checkbox("Show Enemy Player Names", ref showPlayerNames))
            {
                Configuration.ShowPlayerNames = showPlayerNames;
                Configuration.Save();
            }
            if (ImGui.IsItemHovered())
            {
                ImGui.SetTooltip("Displays character names above enemy players.\nUseful for recognizing specific players and coordinating with team.\nDisable to reduce screen clutter.");
            }

            var showStatusEffects = Configuration.ShowStatusEffects;
            if (ImGui.Checkbox("Show Status Effects", ref showStatusEffects))
            {
                Configuration.ShowStatusEffects = showStatusEffects;
                Configuration.Save();
            }
            if (ImGui.IsItemHovered())
            {
                ImGui.SetTooltip("Displays important buffs and debuffs on players.\nShows crowd control, vulnerability, damage buffs, and other tactical information.\nHelps identify opportunities and threats.");
            }

            if (Configuration.ShowStatusEffects)
            {
                ImGui.Indent();
                var showOnlyImportant = Configuration.ShowOnlyImportantStatus;
                if (ImGui.Checkbox("Show Only Negative Effects", ref showOnlyImportant))
                {
                    Configuration.ShowOnlyImportantStatus = showOnlyImportant;
                    Configuration.Save();
                }
                if (ImGui.IsItemHovered())
                {
                    ImGui.SetTooltip("When enabled, only shows debuffs and negative status effects.\nWhen disabled, shows both buffs and debuffs.\nRecommended: Enable to reduce visual clutter.");
                }
                ImGui.Unindent();
            }

            ImGui.Spacing();
            ImGui.Separator();
            ImGui.Spacing();

            var showDefensiveBuffs = Configuration.ShowDefensiveBuffs;
            if (ImGui.Checkbox("Show Defensive Buff Indicators", ref showDefensiveBuffs))
            {
                Configuration.ShowDefensiveBuffs = showDefensiveBuffs;
                Configuration.Save();
            }
            if (ImGui.IsItemHovered())
            {
                ImGui.SetTooltip("Shows special indicators for enemies using defensive abilities.\n" +
                                "• GUARD - AVOID: Enemy has 90% damage reduction (blue line)\n" +
                                "• HEALING: Enemy is using Recuperate (cyan line)\n" +
                                "• DEFENSIVE: Enemy has other defensive buffs (light blue line)\n" +
                                "Helps you avoid wasting damage on protected enemies.");
            }

            ImGui.EndTabItem();
        }
    }

    private void DrawEnemyAllyTab()
    {
        if (ImGui.BeginTabItem("Enemy/Ally"))
        {
            ImGui.TextWrapped("Configure how the plugin distinguishes between enemies and allies.");
            ImGui.Spacing();

            var showEnemies = Configuration.ShowEnemies;
            if (ImGui.Checkbox("Show Lines to Enemies", ref showEnemies))
            {
                Configuration.ShowEnemies = showEnemies;
                Configuration.Save();
            }
            if (ImGui.IsItemHovered())
            {
                ImGui.SetTooltip("Draw lines to enemy players.\nThis is the main feature for tracking opponents in PvP.");
            }

            var showAllies = Configuration.ShowAllies;
            if (ImGui.Checkbox("Show Lines to Allies", ref showAllies))
            {
                Configuration.ShowAllies = showAllies;
                Configuration.Save();
            }
            if (ImGui.IsItemHovered())
            {
                ImGui.SetTooltip("Draw lines to allied players (party/alliance members).\nUseful for keeping track of your team's position.");
            }

            ImGui.Spacing();
            ImGui.Separator();
            ImGui.Spacing();

            var showIndicator = Configuration.ShowAllyEnemyIndicator;
            if (ImGui.Checkbox("Show 'ALLY' / 'ENEMY' Labels", ref showIndicator))
            {
                Configuration.ShowAllyEnemyIndicator = showIndicator;
                Configuration.Save();
            }
            if (ImGui.IsItemHovered())
            {
                ImGui.SetTooltip("Display text labels above players indicating if they are allies or enemies.\nHelps quickly identify player status in chaotic battles.");
            }

            ImGui.Spacing();
            ImGui.Text("Ally Line Appearance:");

            var allyLineColor = Configuration.AllyLineColor;
            if (ImGui.ColorEdit4("Ally Line Color", ref allyLineColor))
            {
                Configuration.AllyLineColor = allyLineColor;
                Configuration.Save();
            }
            if (ImGui.IsItemHovered())
            {
                ImGui.SetTooltip("Color for lines to allied players.\nDefault: Green to indicate friendly status.");
            }

            var allyLineThickness = Configuration.AllyLineThickness;
            if (ImGui.SliderFloat("Ally Line Thickness", ref allyLineThickness, 1.0f, 10.0f))
            {
                Configuration.AllyLineThickness = allyLineThickness;
                Configuration.Save();
            }
            if (ImGui.IsItemHovered())
            {
                ImGui.SetTooltip("Thickness of lines to allied players.\nCan be different from enemy line thickness for easy distinction.");
            }

            var differentColors = Configuration.DifferentColorsForAllies;
            if (ImGui.Checkbox("Use Different Colors for Allies", ref differentColors))
            {
                Configuration.DifferentColorsForAllies = differentColors;
                Configuration.Save();
            }
            if (ImGui.IsItemHovered())
            {
                ImGui.SetTooltip("When enabled, ally lines will use a blend of role colors and ally color.\nWhen disabled, all ally lines use the same ally color.");
            }

            ImGui.Spacing();
            ImGui.Separator();
            ImGui.Spacing();

            ImGui.TextWrapped("Status Effect Information:");
            ImGui.BulletText("Vulnerable enemies get orange lines for easy targeting");
            ImGui.BulletText("Crowd controlled enemies have pulsing status text");
            ImGui.BulletText("Status effects show remaining duration when available");
            ImGui.BulletText("Priority effects (stuns, vulnerability) are shown first");

            ImGui.Spacing();
            ImGui.TextWrapped("Note: Ally detection works for party and alliance members. In some PvP modes, additional allies (like Grand Company members) may not be automatically detected.");

            ImGui.EndTabItem();
        }
    }

    private void DrawJobRoleTab()
    {
        if (ImGui.BeginTabItem("Jobs & Roles"))
        {
            ImGui.TextWrapped("Configure job and role detection for tactical PvP advantage.");
            ImGui.Spacing();

            var showPlayerJobs = Configuration.ShowPlayerJobs;
            if (ImGui.Checkbox("Show Enemy Jobs/Roles", ref showPlayerJobs))
            {
                Configuration.ShowPlayerJobs = showPlayerJobs;
                Configuration.Save();
            }
            if (ImGui.IsItemHovered())
            {
                ImGui.SetTooltip("Displays job or role information above enemy players.\nHelps identify threats: Healers (priority targets), Tanks (high HP), DPS types.\nEssential for tactical PvP play.");
            }

            if (Configuration.ShowPlayerJobs)
            {
                ImGui.Indent();
                var showJobIcons = Configuration.ShowJobIcons;
                if (ImGui.Checkbox("Show Job Abbreviations (vs Role Names)", ref showJobIcons))
                {
                    Configuration.ShowJobIcons = showJobIcons;
                    Configuration.Save();
                }
                if (ImGui.IsItemHovered())
                {
                    ImGui.SetTooltip("Job Abbreviations: WHM, PLD, NIN, etc.\nRole Names: HEAL, TANK, MDPS, etc.\nJob abbreviations are more specific but take more space.");
                }
                ImGui.Unindent();
            }

            var colorCodeByRole = Configuration.ColorCodeByRole;
            if (ImGui.Checkbox("Color Lines by Role", ref colorCodeByRole))
            {
                Configuration.ColorCodeByRole = colorCodeByRole;
                Configuration.Save();
            }
            if (ImGui.IsItemHovered())
            {
                ImGui.SetTooltip("Colors lines based on enemy role:\nBlue = Tanks, Green = Healers, Red = Melee DPS\nOrange = Ranged Physical DPS, Purple = Magical DPS\nOverrides the custom line color setting when enabled.");
            }

            ImGui.Spacing();
            ImGui.Separator();
            ImGui.Spacing();

            // Role color preview
            ImGui.TextWrapped("Role Color Preview:");
            ImGui.Spacing();

            ImGui.TextColored(JobHelper.GetRoleColor(PlayerRole.Tank), "■ TANK - Tanks (PLD, WAR, DRK, GNB)");
            ImGui.TextColored(JobHelper.GetRoleColor(PlayerRole.Healer), "■ HEAL - Healers (WHM, SCH, AST, SGE)");
            ImGui.TextColored(JobHelper.GetRoleColor(PlayerRole.MeleeDPS), "■ MDPS - Melee DPS (MNK, DRG, NIN, SAM, RPR, VPR)");
            ImGui.TextColored(JobHelper.GetRoleColor(PlayerRole.PhysicalRangedDPS), "■ PDPS - Physical Ranged (BRD, MCH, DNC)");
            ImGui.TextColored(JobHelper.GetRoleColor(PlayerRole.MagicalRangedDPS), "■ CDPS - Magical Ranged (BLM, SMN, RDM, PCT)");

            ImGui.EndTabItem();
        }
    }

    private void DrawLowHealthTab()
    {
        if (ImGui.BeginTabItem("Low Health"))
        {
            ImGui.TextWrapped("Configure killable target detection and visual indicators.");
            ImGui.Spacing();

            var showLowHealthIndicator = Configuration.ShowLowHealthIndicator;
            if (ImGui.Checkbox("Enable Low Health Indicator", ref showLowHealthIndicator))
            {
                Configuration.ShowLowHealthIndicator = showLowHealthIndicator;
                Configuration.Save();
            }
            if (ImGui.IsItemHovered())
            {
                ImGui.SetTooltip("Highlights enemies with low health as potential kill targets.\nChanges line color, thickness, and adds health information.");
            }

            if (Configuration.ShowLowHealthIndicator)
            {
                ImGui.Spacing();
                ImGui.Separator();
                ImGui.Spacing();

                var lowHealthThreshold = Configuration.LowHealthThreshold;
                if (ImGui.SliderFloat("Low Health Threshold (%)", ref lowHealthThreshold, 5.0f, 50.0f))
                {
                    Configuration.LowHealthThreshold = lowHealthThreshold;
                    Configuration.Save();
                }
                if (ImGui.IsItemHovered())
                {
                    ImGui.SetTooltip("Health percentage below which enemies are marked as 'killable'.\n10-15%: Very low, almost dead\n20-25%: Good for burst combos\n30-40%: Early kill attempt threshold");
                }

                var lowHealthLineColor = Configuration.LowHealthLineColor;
                if (ImGui.ColorEdit4("Low Health Line Color", ref lowHealthLineColor))
                {
                    Configuration.LowHealthLineColor = lowHealthLineColor;
                    Configuration.Save();
                }
                if (ImGui.IsItemHovered())
                {
                    ImGui.SetTooltip("Color for lines to low health enemies.\nRecommended: Gold/Yellow for high visibility, Red for urgency.");
                }

                var lowHealthLineThickness = Configuration.LowHealthLineThickness;
                if (ImGui.SliderFloat("Low Health Line Thickness", ref lowHealthLineThickness, 2.0f, 15.0f))
                {
                    Configuration.LowHealthLineThickness = lowHealthLineThickness;
                    Configuration.Save();
                }
                if (ImGui.IsItemHovered())
                {
                    ImGui.SetTooltip("Thickness of lines to low health enemies.\nThicker lines make killable targets more obvious.");
                }

                var showHealthPercentage = Configuration.ShowHealthPercentage;
                if (ImGui.Checkbox("Show Health Percentage", ref showHealthPercentage))
                {
                    Configuration.ShowHealthPercentage = showHealthPercentage;
                    Configuration.Save();
                }
                if (ImGui.IsItemHovered())
                {
                    ImGui.SetTooltip("Shows exact health percentage (e.g., '15% HP') vs just 'KILLABLE'.\nUseful for precise timing of finishing moves.");
                }

                var pulseKillableTargets = Configuration.PulseKillableTargets;
                if (ImGui.Checkbox("Pulse Killable Targets", ref pulseKillableTargets))
                {
                    Configuration.PulseKillableTargets = pulseKillableTargets;
                    Configuration.Save();
                }
                if (ImGui.IsItemHovered())
                {
                    ImGui.SetTooltip("Makes lines and text for killable targets pulse/animate.\nDraws more attention but may be distracting.");
                }

                ImGui.Spacing();
                ImGui.Separator();
                ImGui.Spacing();

                // Usage tips
                ImGui.TextWrapped("Usage Tips:");
                ImGui.BulletText("Low health targets appear with different colored, thicker lines");
                ImGui.BulletText("Health info appears above the enemy player");
                ImGui.BulletText("Use this to prioritize finishing moves and burst combos");
                ImGui.BulletText("Adjust threshold based on your job's burst potential");
                ImGui.BulletText("Healers at low health are highest priority targets");
            }

            ImGui.EndTabItem();
        }
    }

    private void DrawFocusTargetTab()
    {
        if (ImGui.BeginTabItem("Focus Target"))
        {
            ImGui.TextWrapped("Focus on a specific enemy player with enhanced indicators. Perfect for tracking priority targets in Crystalline Conflict.");
            ImGui.Spacing();

            var enableFocusTarget = Configuration.EnableFocusTarget;
            if (ImGui.Checkbox("Enable Focus Target", ref enableFocusTarget))
            {
                Configuration.EnableFocusTarget = enableFocusTarget;
                Configuration.Save();
            }
            if (ImGui.IsItemHovered())
            {
                ImGui.SetTooltip("Enable the focus target system to track specific enemy players with special indicators.");
            }

            if (Configuration.EnableFocusTarget)
            {
                ImGui.Spacing();
                ImGui.Separator();
                ImGui.Spacing();

                ImGui.Text("Target Selection:");
                var focusTargetName = Configuration.FocusTargetName;
                if (ImGui.InputText("Player Name", ref focusTargetName, 100))
                {
                    Configuration.FocusTargetName = focusTargetName;
                    Configuration.Save();
                }
                if (ImGui.IsItemHovered())
                {
                    ImGui.SetTooltip("Enter the exact name of the player you want to focus on.\nLeave empty to clear focus target.\nTip: Right-click a player and copy their name.");
                }

                if (ImGui.Button("Clear Focus Target"))
                {
                    Configuration.FocusTargetName = string.Empty;
                    Configuration.Save();
                }
                if (ImGui.IsItemHovered())
                {
                    ImGui.SetTooltip("Remove the current focus target.");
                }

                ImGui.SameLine();
                if (ImGui.Button("Set Current Target"))
                {
                    // This would need to be implemented to get current target
                    ImGui.OpenPopup("SetTargetInfo");
                }
                if (ImGui.IsItemHovered())
                {
                    ImGui.SetTooltip("Set your current in-game target as the focus target.\n(Feature coming soon)");
                }

                ImGui.SameLine();
                if (ImGui.Button("Open Player List"))
                {
                    Plugin.TogglePlayerListUI();
                }
                if (ImGui.IsItemHovered())
                {
                    ImGui.SetTooltip("Open the Player List window to easily select focus targets from nearby players.");
                }

                if (ImGui.BeginPopup("SetTargetInfo"))
                {
                    ImGui.Text("This feature will be implemented to automatically");
                    ImGui.Text("set your current in-game target as focus target.");
                    ImGui.Text("");
                    ImGui.Text("For now, use the Player List window to easily");
                    ImGui.Text("select focus targets from nearby players!");
                    ImGui.EndPopup();
                }

                ImGui.Spacing();
                ImGui.TextWrapped("💡 Tip: Use the Player List window for easy focus target selection! Each player has a 'Focus' button next to their name.");
                ImGui.Spacing();

                ImGui.Spacing();
                ImGui.Separator();
                ImGui.Spacing();

                ImGui.Text("Focus Target Appearance:");

                // Focus target indicator type
                var focusIndicatorType = (int)Configuration.FocusTargetIndicatorType;
                var indicatorNames = new[] { "Lines", "Outlines", "Nameplates", "Icons", "Directional Arrows", "Health Bars", "Combination" };

                if (ImGui.Combo("Focus Indicator Type", ref focusIndicatorType, indicatorNames, indicatorNames.Length))
                {
                    Configuration.FocusTargetIndicatorType = (IndicatorType)focusIndicatorType;
                    Configuration.Save();
                }
                if (ImGui.IsItemHovered())
                {
                    ImGui.SetTooltip("Choose how the focus target appears. This overrides the normal indicator type for your focus target only.");
                }

                var focusTargetColor = Configuration.FocusTargetColor;
                if (ImGui.ColorEdit4("Focus Target Color", ref focusTargetColor))
                {
                    Configuration.FocusTargetColor = focusTargetColor;
                    Configuration.Save();
                }
                if (ImGui.IsItemHovered())
                {
                    ImGui.SetTooltip("Special color for your focus target. Recommended: Bright yellow or orange for high visibility.");
                }

                var focusTargetThickness = Configuration.FocusTargetThickness;
                if (ImGui.SliderFloat("Focus Target Thickness", ref focusTargetThickness, 1.0f, 15.0f))
                {
                    Configuration.FocusTargetThickness = focusTargetThickness;
                    Configuration.Save();
                }
                if (ImGui.IsItemHovered())
                {
                    ImGui.SetTooltip("Make the focus target indicator thicker for better visibility.");
                }

                var focusTargetPulse = Configuration.FocusTargetPulse;
                if (ImGui.Checkbox("Pulse Effect", ref focusTargetPulse))
                {
                    Configuration.FocusTargetPulse = focusTargetPulse;
                    Configuration.Save();
                }
                if (ImGui.IsItemHovered())
                {
                    ImGui.SetTooltip("Add a pulsing animation to the focus target for maximum visibility.");
                }

                var focusTargetAlwaysVisible = Configuration.FocusTargetAlwaysVisible;
                if (ImGui.Checkbox("Always Visible", ref focusTargetAlwaysVisible))
                {
                    Configuration.FocusTargetAlwaysVisible = focusTargetAlwaysVisible;
                    Configuration.Save();
                }
                if (ImGui.IsItemHovered())
                {
                    ImGui.SetTooltip("Show focus target even if they're outside the normal distance range.\nUseful for tracking enemies across the entire Crystalline Conflict map.");
                }

                var showFocusTargetInfo = Configuration.ShowFocusTargetInfo;
                if (ImGui.Checkbox("Show Enhanced Info", ref showFocusTargetInfo))
                {
                    Configuration.ShowFocusTargetInfo = showFocusTargetInfo;
                    Configuration.Save();
                }
                if (ImGui.IsItemHovered())
                {
                    ImGui.SetTooltip("Display additional information for focus target like health percentage, buffs, and distance.");
                }

                ImGui.Spacing();
                ImGui.Separator();
                ImGui.Spacing();

                // Status display
                if (!string.IsNullOrEmpty(Configuration.FocusTargetName))
                {
                    ImGui.Text($"Current Focus Target: {Configuration.FocusTargetName}");
                    ImGui.TextColored(new System.Numerics.Vector4(0, 1, 0, 1), "✓ Focus target is set");
                }
                else
                {
                    ImGui.TextColored(new System.Numerics.Vector4(1, 1, 0, 1), "No focus target set");
                    ImGui.TextWrapped("Enter a player name above to start tracking them.");
                }
            }

            ImGui.EndTabItem();
        }
    }

    private void DrawPlayerListTab()
    {
        if (ImGui.BeginTabItem("Player List"))
        {
            ImGui.TextWrapped("Show a window with all nearby players for easy tracking and focus targeting. Perfect for Crystalline Conflict matches.");
            ImGui.Spacing();

            var showPlayerList = Configuration.ShowPlayerList;
            if (ImGui.Checkbox("Show Player List Window", ref showPlayerList))
            {
                Configuration.ShowPlayerList = showPlayerList;
                Configuration.Save();

                // Toggle the window visibility
                if (showPlayerList)
                    Plugin.TogglePlayerListUI();
            }
            if (ImGui.IsItemHovered())
            {
                ImGui.SetTooltip("Show a separate window listing all nearby players with their status, health, and distance.");
            }

            if (Configuration.ShowPlayerList)
            {
                ImGui.Spacing();
                ImGui.Separator();
                ImGui.Spacing();

                ImGui.Text("Display Options:");

                var playerListShowAllies = Configuration.PlayerListShowAllies;
                if (ImGui.Checkbox("Show Allies Tab", ref playerListShowAllies))
                {
                    Configuration.PlayerListShowAllies = playerListShowAllies;
                    Configuration.Save();
                }

                var playerListShowEnemies = Configuration.PlayerListShowEnemies;
                if (ImGui.Checkbox("Show Enemies Tab", ref playerListShowEnemies))
                {
                    Configuration.PlayerListShowEnemies = playerListShowEnemies;
                    Configuration.Save();
                }

                ImGui.Spacing();

                var playerListShowDistance = Configuration.PlayerListShowDistance;
                if (ImGui.Checkbox("Show Distance", ref playerListShowDistance))
                {
                    Configuration.PlayerListShowDistance = playerListShowDistance;
                    Configuration.Save();
                }

                var playerListShowHealth = Configuration.PlayerListShowHealth;
                if (ImGui.Checkbox("Show Health Percentage", ref playerListShowHealth))
                {
                    Configuration.PlayerListShowHealth = playerListShowHealth;
                    Configuration.Save();
                }

                var playerListShowJob = Configuration.PlayerListShowJob;
                if (ImGui.Checkbox("Show Job/Role", ref playerListShowJob))
                {
                    Configuration.PlayerListShowJob = playerListShowJob;
                    Configuration.Save();
                }

                var playerListShowStatus = Configuration.PlayerListShowStatus;
                if (ImGui.Checkbox("Show Status Effects", ref playerListShowStatus))
                {
                    Configuration.PlayerListShowStatus = playerListShowStatus;
                    Configuration.Save();
                }

                ImGui.Spacing();
                ImGui.Separator();
                ImGui.Spacing();

                ImGui.Text("Behavior Settings:");

                var playerListMaxDistance = Configuration.PlayerListMaxDistance;
                if (ImGui.SliderFloat("Max Distance (yalms)", ref playerListMaxDistance, 30.0f, 200.0f))
                {
                    Configuration.PlayerListMaxDistance = playerListMaxDistance;
                    Configuration.Save();
                }
                if (ImGui.IsItemHovered())
                {
                    ImGui.SetTooltip("Maximum distance to show players in the list.\n100y covers most of a Crystalline Conflict map.");
                }

                var playerListAutoHide = Configuration.PlayerListAutoHide;
                if (ImGui.Checkbox("Auto-hide outside PvP", ref playerListAutoHide))
                {
                    Configuration.PlayerListAutoHide = playerListAutoHide;
                    Configuration.Save();
                }
                if (ImGui.IsItemHovered())
                {
                    ImGui.SetTooltip("Automatically hide the player list when not in PvP zones.");
                }

                var playerListClickToFocus = Configuration.PlayerListClickToFocus;
                if (ImGui.Checkbox("Click to Focus Target", ref playerListClickToFocus))
                {
                    Configuration.PlayerListClickToFocus = playerListClickToFocus;
                    Configuration.Save();
                }
                if (ImGui.IsItemHovered())
                {
                    ImGui.SetTooltip("Click on a player's name in the list to set them as your focus target.");
                }

                ImGui.Spacing();
                ImGui.Separator();
                ImGui.Spacing();

                if (ImGui.Button("Open Player List Window"))
                {
                    Plugin.TogglePlayerListUI();
                }
                if (ImGui.IsItemHovered())
                {
                    ImGui.SetTooltip("Open the player list window to see it in action.");
                }

                ImGui.SameLine();
                if (ImGui.Button("Reset Window Position"))
                {
                    // This would reset the window position - implementation depends on how you want to handle it
                    ImGui.OpenPopup("ResetInfo");
                }
                if (ImGui.IsItemHovered())
                {
                    ImGui.SetTooltip("Reset the player list window to its default position.");
                }

                if (ImGui.BeginPopup("ResetInfo"))
                {
                    ImGui.Text("Close and reopen the player list window to reset its position.");
                    ImGui.EndPopup();
                }
            }

            ImGui.EndTabItem();
        }
    }
}
