using System;
using System.Numerics;
using Dalamud.Interface.Windowing;
using ImGuiNET;

namespace PvPLinePlugin.Windows;

public class MainWindow : Window, IDisposable
{
    private Plugin Plugin;

    public MainWindow(Plugin plugin) : base("PvP Line Plugin###PvPLinePluginMain", ImGuiWindowFlags.NoScrollbar | ImGuiWindowFlags.NoScrollWithMouse)
    {
        SizeConstraints = new WindowSizeConstraints
        {
            MinimumSize = new Vector2(300, 200),
            MaximumSize = new Vector2(float.MaxValue, float.MaxValue)
        };

        Plugin = plugin;
    }

    public void Dispose() { }

    public override void Draw()
    {
        ImGui.Text("PvP Line Plugin");
        ImGui.Separator();

        if (ImGui.Button("Open Configuration"))
        {
            Plugin.ToggleConfigUI();
        }

        ImGui.Spacing();

        var enabled = Plugin.Configuration.Enabled;
        if (ImGui.Checkbox("Enable Lines", ref enabled))
        {
            Plugin.Configuration.Enabled = enabled;
            Plugin.Configuration.Save();
        }

        ImGui.Spacing();
        ImGui.Text("Status:");
        
        if (Plugin.ClientState.LocalPlayer == null)
        {
            ImGui.TextColored(new Vector4(1, 0, 0, 1), "Not logged in");
        }
        else if (!Plugin.Configuration.Enabled)
        {
            ImGui.TextColored(new Vector4(1, 1, 0, 1), "Disabled");
        }
        else
        {
            // Check if in PvP zone
            var territoryType = Plugin.ClientState.TerritoryType;
            var isPvPZone = IsPvPTerritory(territoryType);
            
            if (Plugin.Configuration.OnlyInPvP && !isPvPZone)
            {
                ImGui.TextColored(new Vector4(1, 1, 0, 1), "Not in PvP zone");
            }
            else
            {
                ImGui.TextColored(new Vector4(0, 1, 0, 1), "Active");
            }
        }

        ImGui.Spacing();
        ImGui.Text($"Current Territory: {Plugin.ClientState.TerritoryType}");
        
        if (Plugin.ClientState.LocalPlayer != null)
        {
            ImGui.Text($"Position: {Plugin.ClientState.LocalPlayer.Position}");
        }
    }

    private bool IsPvPTerritory(uint territoryType)
    {
        // Common PvP territory IDs - this is a basic list
        // You may need to expand this based on all PvP zones
        return territoryType switch
        {
            // Frontlines
            376 => true, // Seal Rock
            554 => true, // The Fields of Glory (Shatter)
            692 => true, // The Borderland Ruins (Secure)
            
            // Rival Wings
            691 => true, // Astragalos
            789 => true, // Hidden Gorge
            
            // Crystalline Conflict
            1002 => true, // Crystalline Conflict
            
            // The Wolves' Den
            250 => true, // The Wolves' Den Pier
            
            _ => false
        };
    }
}
