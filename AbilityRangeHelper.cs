using System;
using System.Collections.Generic;
using Dalamud.Game.ClientState.Objects.SubKinds;

namespace PvPLinePlugin;

public static class AbilityRangeHelper
{
    // PvP ability ranges in yalms (approximate values for Crystalline Conflict)
    private static readonly Dictionary<PlayerRole, AbilityRanges> RoleRanges = new()
    {
        [PlayerRole.Tank] = new AbilityRanges
        {
            MeleeRange = 3.0f,
            CastRange = 20.0f,
            MovementRange = 15.0f // Gap closers, charges
        },
        [PlayerRole.MeleeDPS] = new AbilityRanges
        {
            MeleeRange = 3.0f,
            CastRange = 15.0f, // Some ranged abilities
            MovementRange = 20.0f // Gap closers, dashes
        },
        [PlayerRole.PhysicalRangedDPS] = new AbilityRanges
        {
            MeleeRange = 3.0f,
            CastRange = 25.0f,
            MovementRange = 12.0f // Mobility abilities
        },
        [PlayerRole.MagicalRangedDPS] = new AbilityRanges
        {
            MeleeRange = 3.0f,
            CastRange = 25.0f,
            MovementRange = 10.0f // Limited mobility
        },
        [PlayerRole.Healer] = new AbilityRanges
        {
            MeleeRange = 3.0f,
            CastRange = 30.0f, // Healing and damage spells
            MovementRange = 12.0f // Escape abilities
        }
    };

    // Job-specific overrides for more accurate ranges
    private static readonly Dictionary<uint, AbilityRanges> JobSpecificRanges = new()
    {
        // Tanks
        [19] = new AbilityRanges { MeleeRange = 3.0f, CastRange = 15.0f, MovementRange = 20.0f }, // PLD
        [21] = new AbilityRanges { MeleeRange = 3.0f, CastRange = 15.0f, MovementRange = 15.0f }, // WAR
        [32] = new AbilityRanges { MeleeRange = 3.0f, CastRange = 20.0f, MovementRange = 15.0f }, // DRK
        [37] = new AbilityRanges { MeleeRange = 3.0f, CastRange = 15.0f, MovementRange = 20.0f }, // GNB

        // Melee DPS
        [20] = new AbilityRanges { MeleeRange = 3.0f, CastRange = 10.0f, MovementRange = 25.0f }, // MNK
        [22] = new AbilityRanges { MeleeRange = 3.0f, CastRange = 15.0f, MovementRange = 20.0f }, // DRG
        [30] = new AbilityRanges { MeleeRange = 3.0f, CastRange = 12.0f, MovementRange = 30.0f }, // NIN
        [34] = new AbilityRanges { MeleeRange = 3.0f, CastRange = 8.0f, MovementRange = 15.0f },  // SAM
        [39] = new AbilityRanges { MeleeRange = 3.0f, CastRange = 10.0f, MovementRange = 25.0f }, // RPR

        // Ranged Physical DPS
        [23] = new AbilityRanges { MeleeRange = 3.0f, CastRange = 25.0f, MovementRange = 15.0f }, // BRD
        [31] = new AbilityRanges { MeleeRange = 3.0f, CastRange = 25.0f, MovementRange = 12.0f }, // MCH
        [38] = new AbilityRanges { MeleeRange = 3.0f, CastRange = 25.0f, MovementRange = 10.0f }, // DNC

        // Magical DPS
        [25] = new AbilityRanges { MeleeRange = 3.0f, CastRange = 25.0f, MovementRange = 8.0f },  // BLM
        [27] = new AbilityRanges { MeleeRange = 3.0f, CastRange = 25.0f, MovementRange = 12.0f }, // SMN
        [35] = new AbilityRanges { MeleeRange = 3.0f, CastRange = 25.0f, MovementRange = 15.0f }, // RDM

        // Healers
        [24] = new AbilityRanges { MeleeRange = 3.0f, CastRange = 30.0f, MovementRange = 10.0f }, // WHM
        [28] = new AbilityRanges { MeleeRange = 3.0f, CastRange = 30.0f, MovementRange = 12.0f }, // SCH
        [33] = new AbilityRanges { MeleeRange = 3.0f, CastRange = 30.0f, MovementRange = 15.0f }, // AST
        [40] = new AbilityRanges { MeleeRange = 3.0f, CastRange = 25.0f, MovementRange = 12.0f }, // SGE
    };

    public static AbilityRanges GetAbilityRanges(IPlayerCharacter player)
    {
        var jobId = player.ClassJob.RowId;
        
        // Check for job-specific ranges first
        if (JobSpecificRanges.TryGetValue(jobId, out var jobRanges))
        {
            return jobRanges;
        }

        // Fall back to role-based ranges
        var role = JobHelper.GetPlayerRole(player);
        if (RoleRanges.TryGetValue(role, out var roleRanges))
        {
            return roleRanges;
        }

        // Default ranges if nothing else matches
        return new AbilityRanges
        {
            MeleeRange = 3.0f,
            CastRange = 20.0f,
            MovementRange = 12.0f
        };
    }

    public static float GetEffectiveRange(IPlayerCharacter player, RangeType rangeType)
    {
        var ranges = GetAbilityRanges(player);
        
        return rangeType switch
        {
            RangeType.Melee => ranges.MeleeRange,
            RangeType.Cast => ranges.CastRange,
            RangeType.Movement => ranges.MovementRange,
            _ => ranges.CastRange
        };
    }

    public static bool IsPlayerInRange(IPlayerCharacter player, IPlayerCharacter target, RangeType rangeType)
    {
        var range = GetEffectiveRange(player, rangeType);
        var distance = System.Numerics.Vector3.Distance(player.Position, target.Position);
        return distance <= range;
    }

    // Check if player can potentially reach a position with movement abilities
    public static bool CanReachPosition(IPlayerCharacter player, System.Numerics.Vector3 targetPosition)
    {
        var movementRange = GetEffectiveRange(player, RangeType.Movement);
        var distance = System.Numerics.Vector3.Distance(player.Position, targetPosition);
        return distance <= movementRange;
    }
}

public struct AbilityRanges
{
    public float MeleeRange { get; set; }
    public float CastRange { get; set; }
    public float MovementRange { get; set; }
}

public enum RangeType
{
    Melee,
    Cast,
    Movement
}
