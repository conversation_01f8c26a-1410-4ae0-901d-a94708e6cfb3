using Dalamud.Game.Command;
using Dalamud.IoC;
using Dalamud.Plugin;
using Dalamud.Interface.Windowing;
using Dalamud.Plugin.Services;
using Dalamud.Game.ClientState.Objects.SubKinds;
using PvPLinePlugin.Windows;

namespace PvPLinePlugin;

public sealed class Plugin : IDalamudPlugin
{
    [PluginService] internal static IDalamudPluginInterface PluginInterface { get; private set; } = null!;
    [PluginService] internal static ITextureProvider TextureProvider { get; private set; } = null!;
    [PluginService] internal static ICommandManager CommandManager { get; private set; } = null!;
    [PluginService] internal static IClientState ClientState { get; private set; } = null!;
    [PluginService] internal static IDataManager DataManager { get; private set; } = null!;
    [PluginService] internal static IPluginLog Log { get; private set; } = null!;
    [PluginService] internal static IObjectTable ObjectTable { get; private set; } = null!;
    [PluginService] internal static IGameGui GameGui { get; private set; } = null!;
    [PluginService] internal static ICondition Condition { get; private set; } = null!;
    [PluginService] internal static IPartyList PartyList { get; private set; } = null!;

    private const string CommandName = "/pvplines";
    private const string FocusTargetCommandName = "/pvpfocus";

    public Configuration Configuration { get; init; }
    public readonly WindowSystem WindowSystem = new("PvPLinePlugin");
    
    private ConfigWindow ConfigWindow { get; init; }
    private MainWindow MainWindow { get; init; }
    private PlayerListWindow PlayerListWindow { get; init; }
    private PvPOverlay PvPOverlay { get; init; }

    public Plugin()
    {
        Configuration = PluginInterface.GetPluginConfig() as Configuration ?? new Configuration();

        ConfigWindow = new ConfigWindow(this);
        MainWindow = new MainWindow(this);
        PlayerListWindow = new PlayerListWindow(this);
        PvPOverlay = new PvPOverlay(this);

        WindowSystem.AddWindow(ConfigWindow);
        WindowSystem.AddWindow(MainWindow);
        WindowSystem.AddWindow(PlayerListWindow);

        CommandManager.AddHandler(CommandName, new CommandInfo(OnCommand)
        {
            HelpMessage = "Open PvP Line Plugin configuration"
        });

        CommandManager.AddHandler(FocusTargetCommandName, new CommandInfo(OnFocusTargetCommand)
        {
            HelpMessage = "Set current target as focus target"
        });

        PluginInterface.UiBuilder.Draw += DrawUI;
        PluginInterface.UiBuilder.OpenConfigUi += ToggleConfigUI;
        PluginInterface.UiBuilder.OpenMainUi += ToggleMainUI;

        Log.Information($"PvP Line Plugin loaded successfully!");
    }

    public void Dispose()
    {
        WindowSystem.RemoveAllWindows();
        
        ConfigWindow.Dispose();
        MainWindow.Dispose();
        PlayerListWindow.Dispose();
        PvPOverlay.Dispose();

        CommandManager.RemoveHandler(CommandName);
        CommandManager.RemoveHandler(FocusTargetCommandName);
    }

    private void OnCommand(string command, string args)
    {
        // Toggle the main UI or config based on args
        if (args.ToLower() == "config")
        {
            ToggleConfigUI();
        }
        else
        {
            ToggleMainUI();
        }
    }

    private void OnFocusTargetCommand(string command, string args)
    {
        if (!Configuration.EnableFocusTarget || !Configuration.EnableFocusTargetHotkey)
        {
            Log.Info("Focus target hotkey is disabled in configuration.");
            return;
        }

        var currentTarget = ClientState.LocalPlayer?.TargetObject;
        if (currentTarget is IPlayerCharacter targetPlayer)
        {
            var previousTarget = Configuration.FocusTargetName;
            Configuration.FocusTargetName = targetPlayer.Name.TextValue ?? "";
            Configuration.Save();

            if (string.IsNullOrEmpty(previousTarget))
            {
                Log.Info($"Focus target set: {Configuration.FocusTargetName}");
            }
            else
            {
                Log.Info($"Focus target changed: {previousTarget} → {Configuration.FocusTargetName}");
            }
        }
        else
        {
            // Clear focus target if no valid target
            if (!string.IsNullOrEmpty(Configuration.FocusTargetName))
            {
                Configuration.FocusTargetName = "";
                Configuration.Save();
                Log.Info("Focus target cleared (no valid target selected)");
            }
            else
            {
                Log.Info("No player target selected. Right-click on a player and select 'Target' first.");
            }
        }
    }

    private void DrawUI() => WindowSystem.Draw();

    public void ToggleConfigUI() => ConfigWindow.Toggle();
    public void ToggleMainUI() => MainWindow.Toggle();
    public void TogglePlayerListUI() => PlayerListWindow.Toggle();
}
